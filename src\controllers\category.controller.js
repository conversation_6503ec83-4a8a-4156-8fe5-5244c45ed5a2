const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const pick = require('../utils/pick');
const { sendSuccess, sendError } = require('../utils/ApiResponse');
const GlobalService = require('../services/GlobalService');

const modelName = 'Category';
const resourceName = 'Category';
const resourcesName = 'Categories';
const uniqueAttribs = ['name'];
const aggregation = [
  {
    $lookup: {
      from: 'subcategories',
      localField: '_id',
      foreignField: 'category',
      as: 'subCategories',
      pipeline: [
        { $match: { deleted: { $ne: true } } },
        { $project: { __v: 0, deleted: 0,category: 0, createdAt:0, updatedAt:0 } }, // Exclude __v and deleted fields
      ],
    },
  },
];

// Create a new category
const create = catchAsync(async (req, res) => {
  let category = await GlobalService.create(modelName, pick(req.body, ['name' , 'image', 'description']));
  let result = {};
  let subCategories = [];
  if (Array.isArray(req.body.subCategories)) {
    for (const subCategoryItem of req.body.subCategories) {
      subCategoryItem['category'] = category._id;
      const createdSubCategoryItem = await GlobalService.create('SubCategory', subCategoryItem); // Use a different variable
      subCategories.push(createdSubCategoryItem);
    }
  }
  result = { ...category._doc, subCategories };
  sendSuccess(res, `${resourceName} created successfully!`, httpStatus.CREATED, result);
});

// Get all categories
const index = catchAsync(async (req, res) => {
  const options = pick(req.query, ['sortBy', 'limit', 'page']);
  const category = await GlobalService.getAll(modelName, options, aggregation);
  sendSuccess(res, `${resourcesName} fetched successfully!`, httpStatus.OK, category);
});

// Get a particular category
const view = catchAsync(async (req, res) => {
  const category = await GlobalService.getById(modelName, req.params.category, aggregation);
  if (!category) sendError(res, `${resourceName} not found`, httpStatus.NOT_FOUND);
  sendSuccess(res, `${resourceName} fetched successfully!`, httpStatus.OK, category);
});

// Update a particular category with subcategories
const update = catchAsync(async (req, res) => {
  const categoryId = req.body.category;
  let category = await GlobalService.getById(modelName, categoryId, aggregation);
  if (!category) return sendError(res, `${resourceName} not found`, httpStatus.NOT_FOUND);

  const updatedSubCategories = req.body.subCategories || [];
  let subCategories = []; // To store the updated subcategories

  // Step 1: Remove subcategories that are not in the update list
  const subCategoryIdsToRemove = category.subCategories.filter(
    (subCategory) =>
      !updatedSubCategories.some((updated) => updated._id && updated._id.toString() === subCategory._id.toString())
  );

  for (const subCategoryToRemove of subCategoryIdsToRemove) {
    await GlobalService.softDeleteById('SubCategory', uniqueAttribs, subCategoryToRemove._id);
  }

  // Step 2: Add new subcategories or update existing ones
  let count = 0;
  for (const newSubCategory of updatedSubCategories) {
    if (!newSubCategory._id) {
      // If no _id, this is a new subcategory to create
      newSubCategory['category'] = categoryId;
      const createdSubCategory = await GlobalService.create('SubCategory', newSubCategory);
      subCategories.push(createdSubCategory); // Add created subcategory to the list
    } else {
      const existingSubCategory = category.subCategories.find(
        (subCategory) => subCategory._id.toString() === newSubCategory._id.toString()
      );

      if (existingSubCategory) {
        // Update existing subcategory
        const updatedSubCategory = await GlobalService.updateById('SubCategory', newSubCategory._id, newSubCategory);
        subCategories.push(updatedSubCategory); // Add updated subcategory to the list
      }
    }
    count++;
  }

  // Step 3: Update the category itself (only if needed)
  category = await GlobalService.updateById(modelName, categoryId, pick(req.body, ['name', 'status']), aggregation);

  sendSuccess(res, `${resourceName} updated successfully!`, httpStatus.OK, category);
});

// Toggle status of a particular category
const status = catchAsync(async (req, res) => {
  const category = await GlobalService.toggleStatusById(modelName, req.body.category);
  sendSuccess(res, `${resourceName} status changed successfully!`, httpStatus.OK, category);
});

// Soft delete a particular category
const softDelete = catchAsync(async (req, res) => {
  const category = await GlobalService.getById(modelName, req.body.category, aggregation);
  if (!category) return sendError(res, `${resourceName} not found`, httpStatus.NOT_FOUND);
  for (const subCategoryItem of category.subCategories) {
    await GlobalService.softDeleteById('SubCategory', uniqueAttribs, subCategoryItem._id);
  }
  await GlobalService.softDeleteById(modelName, uniqueAttribs, req.body.category);
  sendSuccess(res, `${resourceName} deleted successfully!`, httpStatus.NO_CONTENT);
});

// Toggle status of a particular sub category
const subCategoryStatus = catchAsync(async (req, res) => {
  const subCategory = await GlobalService.toggleStatusById('SubCategory', req.body.subCategory);
  sendSuccess(res, `Sub Category status changed successfully!`, httpStatus.OK, subCategory);
});

// Soft delete a particular sub category
const subCategorySoftDelete = catchAsync(async (req, res) => {
  await GlobalService.softDeleteById('SubCategory', uniqueAttribs, req.body.subCategory);
  sendSuccess(res, `Sub Category deleted successfully!`, httpStatus.NO_CONTENT);
});

// Get all active categories and active sub categories without pagination
const fetch = catchAsync(async (req, res) => {
  const aggregation1 = [
    {
      $match: {
        status: true, // Filter only active categories
      },
    },
    {
      $lookup: {
        from: 'subcategories', // Name of the referenced collection
        localField: '_id', // Field in the category collection
        foreignField: 'category', // Field in the subcategories collection
        as: 'subCategories', // Output field name
        pipeline: [
          { $match: { status: true } }, // Filter only active subcategories
          { $match: { deleted: { $ne: true } } },
        ],
      },
    },
  ];
  const category = await GlobalService.getAll(modelName, aggregation1);
  sendSuccess(res, `${resourcesName} fetched successfully!`, httpStatus.OK, category);
});

module.exports = { create, index, fetch, view, update, status, softDelete, subCategoryStatus, subCategorySoftDelete };
